package adapters

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"strings"
	"time"

	"github.com/anthropics/anthropic-sdk-go"
	"github.com/anthropics/anthropic-sdk-go/option"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/proto"
)

type <PERSON> struct {
	client anthropic.Client
}

func NewClaudeClient(baseUrl, apiKey string) *<PERSON> {
	return &Claude{
		client: anthropic.NewClient(
			option.WithBaseURL(baseUrl),
			option.WithAPIKey(apiKey),
		),
	}
}

/*
*

	{
	  "id" : "msg_01K2WV79J7nLF2Awec8BiKfX",
	  "type" : "message",
	  "role" : "assistant",
	  "model" : "claude-sonnet-4-20250514",
	  "content" : [ {
	    "type" : "text",
	    "text" : "A quaternion is a mathematical system that extends complex numbers and is commonly used to represent rotations in 3D space. Here's an overview:\n\n## Mathematical Structure\n\nA quaternion is typically written as:\n**q = w + xi + yj + zk**\n\nWhere:\n- w, x, y, z are real numbers\n- i, j, k are the fundamental quaternion units\n\nThe fundamental units follow these multiplication rules:\n- i² = j² = k² = ijk = -1\n- ij = k, ji = -k\n- jk = i, kj = -i  \n- ki = j, ik = -j\n\n## Components\n\n- **w**: the scalar (real) part\n- **xi + yj + zk**: the vector (imaginary) part\n\n## Key Properties\n\n- **4D number system**: Quaternions form a 4-dimensional number system\n- **Non-commutative**: Unlike real/complex numbers, quaternion multiplication order matters (qp ≠ pq)\n- **Unit quaternions**: When |q| = 1, they efficiently represent 3D rotations\n\n## Main Applications\n\n1. **3D Computer Graphics**: Rotating objects, cameras, and coordinate systems\n2. **Robotics**: Controlling robot arm orientations and movements\n3. **Game Development**: Character and object rotations\n4. **Aerospace**: Spacecraft attitude control\n5. **Animation**: Smooth interpolation between rotations\n\n## Advantages for Rotations\n\n- **Compact**: Only 4 numbers vs 9 for rotation matrices\n- **No gimbal lock**: Unlike Euler angles\n- **Smooth interpolation**: Easy to blend between rotations\n- **Efficient composition**: Fast rotation combinations\n\nQuaternions were invented by Irish mathematician William Rowan Hamilton in 1843 and have become essential tools in modern 3D mathematics and computer graphics."
	  } ],
	  "stop_reason" : "end_turn",
	  "stop_sequence" : null,
	  "usage" : {
	    "input_tokens" : 13,
	    "cache_creation_input_tokens" : 0,
	    "cache_read_input_tokens" : 0,
	    "output_tokens" : 426,
	    "service_tier" : "standard"
	  }
	}
*/

func (c *Claude) ChatCompletion(ctx context.Context, req *proto.ChatCompletionRequest, model *models.GWModelConfig) (*proto.ChatCompletionResponse, error) {
	max_token := model.MaxTokens
	if req.MaxTokens != nil && *req.MaxTokens < max_token {
		max_token = *req.MaxTokens
	}
	params := anthropic.MessageNewParams{
		MaxTokens: int64(max_token),
		Model:     anthropic.Model(model.ModelName),
	}
	if req.Temperature != nil {
		params.Temperature = anthropic.Float(*req.Temperature)
	}

	// Convert messages and extract system prompt
	systemPrompts, messages, err := c.convertMessages(req.Messages)
	if err != nil {
		return nil, fmt.Errorf("failed to convert messages: %w", err)
	}
	params.System = systemPrompts
	params.Messages = messages

	// Handle tools
	if len(req.Tools) > 0 {
		tools := make([]anthropic.ToolUnionParam, len(req.Tools))
		for i, tool := range req.Tools {
			claudeTool := &anthropic.ToolParam{
				InputSchema: c.getToolParameter(tool.Function.Parameters),
				Name:        tool.Function.Name,
				Description: anthropic.String(tool.Function.Description),
			}
			if tool.Function.Cached {
				claudeTool.CacheControl = anthropic.NewCacheControlEphemeralParam()
			}
			tools[i] = anthropic.ToolUnionParam{OfTool: claudeTool}
		}
		params.Tools = tools

		// Handle tool choice
		if req.ToolChoice != nil {
			params.ToolChoice = c.convertToolChoice(*req.ToolChoice)
		}
	}
	res, err := c.client.Messages.New(ctx, params)
	if err != nil {
		/**
		{
		  "type" : "error",
		  "error" : {
		    "type" : "authentication_error",
		    "message" : "invalid x-api-key"
		  }
		}
		*/
		var apiErr *anthropic.Error
		if errors.As(err, &apiErr) {
			var protoErr proto.ErrorResponse
			jsoniter.UnmarshalFromString(apiErr.RawJSON(), &protoErr)
			return nil, &protoErr
		}
		return nil, fmt.Errorf("claude API call failed: %w", err)
	}

	// Process response content
	content, toolCalls, err := c.processResponseContent(res.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to process response content: %w", err)
	}

	chat_message := proto.ChatMessage{
		Role:      string(res.Role),
		Content:   content,
		ToolCalls: toolCalls,
	}

	resp := &proto.ChatCompletionResponse{
		Raw:     []byte(res.RawJSON()),
		ID:      res.ID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   string(res.Model),
		Choices: []proto.ChatCompletionChoice{
			{
				Index:        0,
				Message:      chat_message,
				FinishReason: string(res.StopReason),
			},
		},
		Usage: &proto.ChatCompletionUsage{
			PromptTokens:      res.Usage.InputTokens,
			CompletionTokens:  res.Usage.OutputTokens,
			TotalTokens:       res.Usage.InputTokens + res.Usage.OutputTokens,
			CacheReadTokens:   res.Usage.CacheReadInputTokens,
			CacheCreateTokens: res.Usage.CacheCreationInputTokens,
		},
	}

	return resp, nil
}

// convertMessages converts OpenAI format messages to Claude format
func (c *Claude) convertMessages(messages []proto.ChatMessage) ([]anthropic.TextBlockParam, []anthropic.MessageParam, error) {
	var systemPrompt string
	var claudeMessages []anthropic.MessageParam

	for _, msg := range messages {
		switch msg.Role {
		case "system":
			// Claude handles system messages separately
			if content, ok := msg.Content.(string); ok {
				if systemPrompt != "" {
					systemPrompt += "\n\n" + content
				} else {
					systemPrompt = content
				}
			}

		case "user", "assistant":
			claudeMsg, err := c.convertMessage(msg)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to convert message: %w", err)
			}
			claudeMessages = append(claudeMessages, claudeMsg)
		}
	}

	return systemPrompt, claudeMessages, nil
}

// convertMessage converts a single OpenAI message to Claude format
func (c *Claude) convertMessage(msg proto.ChatMessage) (anthropic.MessageParam, error) {
	// Handle different content types
	switch content := msg.Content.(type) {
	case string:
		// Simple text content
		if msg.Role == "user" {
			return anthropic.NewUserMessage(anthropic.NewTextBlock(content)), nil
		} else {
			return anthropic.NewAssistantMessage(anthropic.NewTextBlock(content)), nil
		}
	case []interface{}:
		// Multi-part content (text + images)
		var contentBlocks []anthropic.ContentBlockParamUnion
		for _, part := range content {
			if partMap, ok := part.(map[string]interface{}); ok {
				if partType, exists := partMap["type"]; exists && partType == "text" {
					if text, exists := partMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							block := anthropic.TextBlockParam{
								Text: textStr,
							}
							if partMap["cached"] != nil && partMap["cached"].(bool) {
								block.CacheControl = anthropic.NewCacheControlEphemeralParam()
							}
							contentBlocks = append(contentBlocks, anthropic.ContentBlockParamUnion{OfText: &block})
						}
					}
				}
				// TODO: Handle image_url type if needed
			}
		}
		if len(contentBlocks) > 0 {
			if msg.Role == "user" {
				return anthropic.NewUserMessage(contentBlocks...), nil
			} else {
				return anthropic.NewAssistantMessage(contentBlocks...), nil
			}
		}
	default:
		return anthropic.MessageParam{}, fmt.Errorf("unsupported content type: %T", content)
	}

	// Handle tool calls for assistant messages
	if msg.Role == "assistant" && len(msg.ToolCalls) > 0 {
		var contentBlocks []anthropic.ContentBlockParamUnion

		// Add text content if exists
		if content, ok := msg.Content.(string); ok && content != "" {
			contentBlocks = append(contentBlocks, anthropic.NewTextBlock(content))
		}

		// Add tool use blocks
		for _, toolCall := range msg.ToolCalls {
			var input interface{}
			if toolCall.Function.Arguments != "" {
				if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &input); err != nil {
					return anthropic.MessageParam{}, fmt.Errorf("failed to parse tool arguments: %w", err)
				}
			}

			toolUseBlock := anthropic.ContentBlockParamUnion{
				OfToolUse: &anthropic.ToolUseBlockParam{
					ID:    toolCall.ID,
					Name:  toolCall.Function.Name,
					Input: input,
				},
			}
			contentBlocks = append(contentBlocks, toolUseBlock)
		}

		return anthropic.NewAssistantMessage(contentBlocks...), nil
	}

	// Handle tool call results for user messages
	if msg.Role == "user" && msg.ToolCallID != nil {
		// This is a tool result message
		content := ""
		if contentStr, ok := msg.Content.(string); ok {
			content = contentStr
		}

		toolResultBlock := anthropic.ContentBlockParamUnion{
			OfToolResult: &anthropic.ToolResultBlockParam{
				ToolUseID: *msg.ToolCallID,
				Content: []anthropic.ToolResultBlockParamContentUnion{
					{
						OfText: &anthropic.TextBlockParam{
							Text: content,
						},
					},
				},
			},
		}
		return anthropic.NewUserMessage(toolResultBlock), nil
	}

	return anthropic.MessageParam{}, fmt.Errorf("failed to convert message")
}

// getToolParameter converts OpenAI tool parameters to Claude format
func (c *Claude) getToolParameter(params map[string]interface{}) anthropic.ToolInputSchemaParam {
	// Convert the map to JSON and back to ensure proper structure
	jsonBytes, _ := json.Marshal(params)
	var schema anthropic.ToolInputSchemaParam
	json.Unmarshal(jsonBytes, &schema)
	return schema
}

// convertToolChoice converts OpenAI tool_choice to Claude format
func (c *Claude) convertToolChoice(toolChoice string) anthropic.ToolChoiceUnionParam {
	switch toolChoice {
	case "auto":
		return anthropic.ToolChoiceUnionParam{OfAuto: &anthropic.ToolChoiceAutoParam{}}
	case "none":
		return anthropic.ToolChoiceUnionParam{OfAny: &anthropic.ToolChoiceAnyParam{}}
	default:
		// For specific tool choice, we need to parse it
		// For now, default to auto
		return anthropic.ToolChoiceUnionParam{OfAuto: &anthropic.ToolChoiceAutoParam{}}
	}
}

// processResponseContent processes Claude response content blocks
func (c *Claude) processResponseContent(content []anthropic.ContentBlockUnion) (string, []proto.ToolCall, error) {
	var textParts []string
	var toolCalls []proto.ToolCall

	for _, block := range content {
		switch block := block.AsAny().(type) {
		case anthropic.TextBlock:
			textParts = append(textParts, block.Text)
		case anthropic.ToolUseBlock:
			// Convert tool use to OpenAI format
			inputJSON, err := json.Marshal(block.Input)
			if err != nil {
				return "", nil, fmt.Errorf("failed to marshal tool input: %w", err)
			}

			toolCall := proto.ToolCall{
				ID:   block.ID,
				Type: "function",
				Function: proto.FunctionCall{
					Name:      block.Name,
					Arguments: string(inputJSON),
				},
			}
			toolCalls = append(toolCalls, toolCall)
		}
	}

	content_text := strings.Join(textParts, "")
	return content_text, toolCalls, nil
}

func (c *Claude) parseContentBlockParamUnion(content interface{}) ([]anthropic.ContentBlockParamUnion, error) {
	if content == nil {
		return nil, nil
	}
	var contentBlocks []anthropic.ContentBlockParamUnion
	if contentStr, ok := content.(string); ok {
		contentBlocks = append(contentBlocks, anthropic.NewTextBlock(contentStr))
		return contentBlocks, nil
	}
	lst, ok := content.([]interface{})
	if !ok {
		return nil, fmt.Errorf("unsupported content type: %T", content)
	}
	for _, part := range lst {
		if partMap, ok := part.(map[string]interface{}); ok {
			if partType, exists := partMap["type"]; exists && partType == "text" {
				if text, exists := partMap["text"]; exists {
					if textStr, ok := text.(string); ok {
						block := anthropic.TextBlockParam{
							Text: textStr,
						}
						if partMap["cached"] != nil && partMap["cached"].(bool) {
							block.CacheControl = anthropic.NewCacheControlEphemeralParam()
						}
						contentBlocks = append(contentBlocks, anthropic.ContentBlockParamUnion{OfText: &block})
					}
				}
			}
		}
	}
	return contentBlocks, nil
}
