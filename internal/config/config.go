package config

import (
	"fmt"
	"goalfy_aigateway/pkg/otel_init"
	"net/http"
	"net/url"
	"sync"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
)

// AdminConfig 管理员配置
type AdminConfig struct {
	LoginKey  string
	JWTSecret string
}

// Config 定义总配置结构
type Config struct {
	Database mysql.Config
	Admin    AdminConfig
	Otel     otel_init.OtelConfig
}

var (
	config     *Config
	configLock sync.RWMutex
)

// LoadConfig 从文件加载配置
func LoadConfig(path string) error {

	viper.SetConfigFile(path)
	err := viper.ReadInConfig()
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}
	if err := viper.Unmarshal(&config); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	urL, _ := url.Parse("http://localhost:7890")
	http.DefaultClient.Transport = &http.Transport{
		Proxy: http.ProxyURL(urL),
	}
	
	return nil
}

// GetConfig 获取当前配置
func GetConfig() *Config {
	configLock.RLock()
	defer configLock.RUnlock()
	return config
}
